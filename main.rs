use anyhow::Result;
use bot::ArbitrageBot;
use env_logger::Builder;
use log::LevelFilter;
use std::io::Write;
use std::time::Duration;

mod bot;
mod consts;
mod types;
mod jito_tip_manager;
mod alt_cache_manager;
mod blockhash_cache_manager;
mod price_cache_manager;
mod IpRotator;

#[tokio::main]
async fn main() -> Result<()> {
    // Initialize logger with timestamp
    Builder::new()
        .format(|buf, record| {
            writeln!(
                buf,
                "{} [{}] {}",
                chrono::Local::now().format("%Y-%m-%d %H:%M:%S"),
                record.level(),
                record.args()
            )
        })
        .filter(None, LevelFilter::Info)
        .init();

    dotenv::dotenv().ok();

    let bot = ArbitrageBot::new()?;
    
    // 初始化IP轮询器
    log::info!("开始初始化IP轮询器...");
    if let Err(e) = bot.initialize_ip_rotator().await {
        log::error!("初始化IP轮询器失败: {}", e);
    }
    
    // 初始化区块哈希缓存
    log::info!("开始初始化区块哈希缓存...");
    if let Err(e) = bot.initialize_blockhash_cache().await {
        log::error!("初始化区块哈希缓存失败: {}", e);
    }
    
    // 初始化价格缓存管理器
    log::info!("开始初始化价格缓存管理器...");
    if let Err(e) = bot.initialize_price_cache().await {
        log::error!("初始化价格缓存管理器失败: {}", e);
    }
    
    // 预热 ALT 缓存
    log::info!("开始预热 ALT 缓存...");
    if let Err(e) = bot.preload_alt_cache().await {
        log::error!("预热 ALT 缓存失败: {}", e);
    }

    loop {
        if let Err(e) = bot.run().await {
            log::error!("Error running bot: {}", e);
        }
        tokio::time::sleep(Duration::from_millis(1)).await;
    }
}
