use anyhow::{Context, Result};
use local_ip_address::{list_afinet_netifas, local_ip};
use log::{debug, error, info, warn};
use reqwest::{Client, ClientBuilder};
use serde_json::Value;
use std::collections::HashMap;
use std::net::IpAddr;
use std::sync::{Arc, Mutex};
use std::time::{Duration, Instant};
use tokio::process::Command;

/// IP轮询器 - 管理多个本地IP地址并轮询使用
pub struct IpRotator {
    available_ips: Arc<Mutex<Vec<IpAddr>>>,
    current_index: Arc<Mutex<usize>>,
    // 为每个IP缓存一个HTTP客户端
    client_cache: Arc<Mutex<HashMap<IpAddr, Client>>>,
    default_client: Client,
}

impl IpRotator {
    /// 创建新的IP轮询器实例
    pub fn new() -> Self {
        let default_client = ClientBuilder::new()
            .timeout(Duration::from_secs(5))
            .build()
            .expect("Failed to build HTTP client");

        Self {
            available_ips: Arc::new(Mutex::new(Vec::new())),
            current_index: Arc::new(Mutex::new(0)),
            client_cache: Arc::new(Mutex::new(HashMap::new())),
            default_client,
        }
    }

    /// 初始化IP轮询器
    pub async fn initialize(&self) -> Result<()> {
        info!("开始扫描系统IPv4地址...");
        self.scan_system_ips().await?;

        // 如果没有找到可用IP，使用默认IP
        {
            let ips = self.available_ips.lock().unwrap();
            if ips.is_empty() {
                drop(ips); // 释放锁
                let default_ip = local_ip()?;
                info!("未找到任何可用IP地址，将使用默认IP: {}", default_ip);
                let mut ips = self.available_ips.lock().unwrap();
                ips.push(default_ip);
            } else {
                info!("IP扫描完成，共发现 {} 个IP地址:", ips.len());
                for (i, ip) in ips.iter().enumerate() {
                    info!("[{}] {}", i + 1, ip);
                }
            }
        }

        // 测试IP连通性
        self.test_ip_connectivity().await?;
        
        // 为每个可用IP预创建HTTP客户端
        self.preload_http_clients().await?;
        
        Ok(())
    }

    /// 为每个可用IP预创建HTTP客户端
    async fn preload_http_clients(&self) -> Result<()> {
        let start_time = Instant::now();
        info!("开始为所有IP地址预创建HTTP客户端...");
        
        let ips = self.available_ips.lock().unwrap().clone();
        let mut clients = self.client_cache.lock().unwrap();
        
        for ip in ips {
            let client = ClientBuilder::new()
                .local_address(ip)
                .timeout(Duration::from_secs(5))
                .build()?;
                
            clients.insert(ip, client);
            info!("为IP {} 创建了HTTP客户端", ip);
        }
        
        let duration = start_time.elapsed();
        info!("HTTP客户端预创建完成，耗时: {}ms", duration.as_millis());
        Ok(())
    }

    /// 扫描系统上的所有IPv4地址
    async fn scan_system_ips(&self) -> Result<()> {
        // 使用network_interface库获取所有网络接口
        let network_interfaces = list_afinet_netifas()?;
        
        let mut ips = self.available_ips.lock().unwrap();
        ips.clear();

        for (name, ip) in network_interfaces.iter() {
            // 只收集IPv4且非内部地址
            if ip.is_ipv4() && !ip.is_loopback() {
                ips.push(*ip);
                info!("发现IP地址: {} ({})", ip, name);
            }
        }

        // 在Linux系统上尝试使用命令行获取更多IP
        if cfg!(target_os = "linux") {
            match self.get_linux_ips().await {
                Ok(linux_ips) => {
                    for ip in linux_ips {
                        if !ips.contains(&ip) {
                            ips.push(ip);
                            info!("发现额外IP地址: {} (通过ip命令)", ip);
                        }
                    }
                }
                Err(e) => warn!("无法使用Linux命令获取额外IP地址: {}", e),
            }
        }

        Ok(())
    }

    /// 在Linux系统上使用ip命令获取所有IP地址
    async fn get_linux_ips(&self) -> Result<Vec<IpAddr>> {
        let output = Command::new("sh")
            .arg("-c")
            .arg("ip -4 addr show | grep inet | awk '{print $2}' | cut -d/ -f1")
            .output()
            .await?;

        let stdout = String::from_utf8(output.stdout)?;
        let mut ips = Vec::new();

        for line in stdout.lines() {
            let ip = line.trim();
            if !ip.is_empty() && ip != "127.0.0.1" {
                match ip.parse::<IpAddr>() {
                    Ok(addr) => ips.push(addr),
                    Err(e) => warn!("无法解析IP地址 {}: {}", ip, e),
                }
            }
        }

        Ok(ips)
    }

    /// 测试所有IP的连通性
    async fn test_ip_connectivity(&self) -> Result<()> {
        info!("开始测试IP连通性...");
        let mut connected_ips = Vec::new();
        
        let ips = self.available_ips.lock().unwrap().clone();
        
        for ip in ips {
            info!("测试IP: {}", ip);
            
            // 创建绑定到特定IP的HTTP客户端
            let client = reqwest::Client::builder()
                .local_address(ip)
                .timeout(Duration::from_secs(5))
                .build()?;
            
            // 尝试使用此IP发送请求到一个公共API
            let start_time = Instant::now();
            match client.get("https://api.ipify.org?format=json").send().await {
                Ok(response) => {
                    let duration = start_time.elapsed();
                    if let Ok(json) = response.json::<Value>().await {
                        info!("✅ IP {} 连接成功，响应时间: {:?}，公网IP: {}", 
                              ip, duration, json["ip"].as_str().unwrap_or("unknown"));
                        connected_ips.push(ip);
                    } else {
                        warn!("❌ IP {} 连接成功但返回无效JSON", ip);
                    }
                }
                Err(e) => {
                    warn!("❌ IP {} 连接失败: {}", ip, e);
                }
            }
        }
        
        // 更新为只包含连通的IP
        {
            let mut available_ips = self.available_ips.lock().unwrap();
            *available_ips = connected_ips.clone();
        }
        
        info!("IP连通性测试完成，{} 个IP地址可用", connected_ips.len());
        Ok(())
    }

    /// 获取下一个IP地址（轮询方式）
    pub fn get_next_ip(&self) -> Option<IpAddr> {
        let ips = self.available_ips.lock().unwrap();
        if ips.is_empty() {
            return None;
        }
        
        let mut index = self.current_index.lock().unwrap();
        let ip = ips[*index];
        *index = (*index + 1) % ips.len();
        
        Some(ip)
    }

    /// 获取所有可用的IP地址
    pub fn get_all_ips(&self) -> Vec<IpAddr> {
        let ips = self.available_ips.lock().unwrap();
        ips.clone()
    }
    
    /// 使用轮询IP发送Jito捆绑包
    pub async fn send_jito_bundle(&self, bundle_data: Value, endpoint: &str) -> Result<Value> {
        // 记录整个函数的开始时间
        let start_total = Instant::now();
        
        // 1. 获取下一个IP地址
        let start_get_ip = Instant::now();
        let ip = match self.get_next_ip() {
            Some(addr) => addr,
            None => return Err(anyhow::anyhow!("没有可用的IP地址")),
        };
        let get_ip_time = start_get_ip.elapsed();
        info!("获取IP地址耗时: {}μs", get_ip_time.as_micros());
        
        info!("使用IP {} 发送Jito捆绑包...", ip);
        
        // 2. 从缓存获取绑定到特定IP的HTTP客户端
        let start_get_client = Instant::now();
        let client = {
            let clients = self.client_cache.lock().unwrap();
            match clients.get(&ip) {
                Some(client) => client.clone(),
                None => {
                    // 如果缓存中没有，使用默认客户端
                    warn!("IP {} 的HTTP客户端未在缓存中找到，使用默认客户端", ip);
                    self.default_client.clone()
                }
            }
        };
        let get_client_time = start_get_client.elapsed();
        info!("获取HTTP客户端耗时: {}μs", get_client_time.as_micros());
            
        // 3. 发送请求
        let start_send_request = Instant::now();
        let response = client
            .post(endpoint)
            .json(&bundle_data)
            .send()
            .await;
        let send_request_time = start_send_request.elapsed();
        info!("发送HTTP请求耗时: {}ms", send_request_time.as_millis());
        
        // 检查响应是否成功
        let response = match response {
            Ok(resp) => resp,
            Err(e) => {
                info!("HTTP请求失败耗时: {}ms", start_send_request.elapsed().as_millis());
                return Err(anyhow::anyhow!("发送请求失败: {}", e));
            }
        };
            
        // 4. 解析响应
        let start_parse_response = Instant::now();
        let response_data = response.json::<Value>().await?;
        let parse_response_time = start_parse_response.elapsed();
        info!("解析响应JSON耗时: {}μs", parse_response_time.as_micros());
        
        // 5. 处理响应结果
        let start_process_result = Instant::now();
        if let Some(result) = response_data.get("result") {
            info!("使用IP {} 成功发送捆绑包，bundle id: {}", ip, result);
        } else if let Some(error) = response_data.get("error") {
            info!("使用IP {} 发送失败，错误: {}", ip, error);
        }
        let process_result_time = start_process_result.elapsed();
        info!("处理响应结果耗时: {}μs", process_result_time.as_micros());
        
        // 记录总耗时
        let total_time = start_total.elapsed();
        info!("发送Jito捆绑包总耗时: {}ms", total_time.as_millis());
        
        Ok(response_data)
    }
}

impl Default for IpRotator {
    fn default() -> Self {
        Self::new()
    }
}
