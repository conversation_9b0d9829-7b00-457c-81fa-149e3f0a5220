use anyhow::Result;
use log::{error, info};
use solana_client::rpc_client::RpcClient;
use solana_sdk::hash::Hash;
use std::{
    sync::{Arc, Mutex, RwLock},
    thread,
    time::{Duration, Instant},
};

/// 区块哈希缓存管理器
pub struct BlockhashCacheManager {
    client: Arc<RpcClient>,
    blockhash: Arc<RwLock<Hash>>,
    last_fetch_time: Arc<RwLock<Instant>>,
    update_interval: Duration,
    is_updating: Arc<Mutex<bool>>,
    is_running: Arc<Mutex<bool>>,
}

impl BlockhashCacheManager {
    /// 创建新的区块哈希缓存管理器
    pub fn new(client: Arc<RpcClient>, update_interval_ms: u64) -> Self {
        Self {
            client,
            blockhash: Arc::new(RwLock::new(Hash::default())),
            last_fetch_time: Arc::new(RwLock::new(Instant::now())),
            update_interval: Duration::from_millis(update_interval_ms),
            is_updating: Arc::new(Mutex::new(false)),
            is_running: Arc::new(Mutex::new(false)),
        }
    }

    /// 初始化区块哈希缓存
    pub async fn initialize(&self) -> Result<()> {
        // 立即获取一次区块哈希
        self.update_blockhash().await?;
        
        // 设置定时器，按指定间隔更新区块哈希
        self.start_periodic_update();
        
        info!("区块哈希缓存初始化完成，更新间隔: {}ms", self.update_interval.as_millis());
        Ok(())
    }

    /// 开始定期更新
    pub fn start_periodic_update(&self) {
        let mut is_running = self.is_running.lock().unwrap();
        if *is_running {
            return;
        }
        *is_running = true;
        
        // 克隆需要在线程中使用的数据
        let client = self.client.clone();
        let update_interval = self.update_interval;
        let blockhash = self.blockhash.clone();
        let last_fetch_time = self.last_fetch_time.clone();
        let is_updating = self.is_updating.clone();
        
        // 创建更新线程
        thread::spawn(move || {
            loop {
                thread::sleep(update_interval);
                
                // 防止重复更新
                let mut updating = is_updating.lock().unwrap();
                if *updating {
                    continue;
                }
                *updating = true;
                
                // 异步更新区块哈希 - 使用多线程 runtime
                let rt = tokio::runtime::Builder::new_multi_thread()
                    .enable_all()
                    .build()
                    .unwrap();
                    
                rt.block_on(async {
                    let start = Instant::now();
                    match client.get_latest_blockhash() {
                        Ok(blockhash_result) => {
                            let duration = start.elapsed();
                            
                            // 更新缓存
                            if let Ok(mut bh) = blockhash.write() {
                                *bh = blockhash_result;
                            }
                            if let Ok(mut lft) = last_fetch_time.write() {
                                *lft = Instant::now();
                            }
                            
                            info!("更新区块哈希: {}, 耗时: {}ms", blockhash_result, duration.as_millis());
                        }
                        Err(e) => {
                            error!("更新区块哈希失败: {}", e);
                        }
                    }
                });
                
                *updating = false;
            }
        });
    }

    /// 停止定期更新
    pub fn stop_periodic_update(&self) {
        let mut is_running = self.is_running.lock().unwrap();
        *is_running = false;
    }

    /// 更新区块哈希
    pub async fn update_blockhash(&self) -> Result<()> {
        let mut is_updating = self.is_updating.lock().unwrap();
        *is_updating = true;
        
        let start = Instant::now();
        let blockhash = self.client.get_latest_blockhash()?;
        let duration = start.elapsed();
        
        {
            let mut bh = self.blockhash.write().unwrap();
            *bh = blockhash;
        }
        {
            let mut lft = self.last_fetch_time.write().unwrap();
            *lft = Instant::now();
        }
        
        info!("更新区块哈希: {}, 耗时: {}ms", blockhash, duration.as_millis());
        
        *is_updating = false;
        Ok(())
    }

    /// 获取缓存的区块哈希
    pub fn get_blockhash(&self) -> Hash {
        let blockhash = self.blockhash.read().unwrap();
        *blockhash
    }

    /// 获取上次更新时间
    pub fn get_last_fetch_time(&self) -> Instant {
        let last_fetch_time = self.last_fetch_time.read().unwrap();
        *last_fetch_time
    }

    /// 获取距离上次更新的时间间隔(毫秒)
    pub fn get_time_since_last_update(&self) -> u128 {
        let last_fetch_time = self.last_fetch_time.read().unwrap();
        last_fetch_time.elapsed().as_millis()
    }

    /// 强制立即更新区块哈希
    pub async fn force_update(&self) -> Result<Hash> {
        self.update_blockhash().await?;
        Ok(self.get_blockhash())
    }
}

impl Drop for BlockhashCacheManager {
    fn drop(&mut self) {
        self.stop_periodic_update();
    }
} 