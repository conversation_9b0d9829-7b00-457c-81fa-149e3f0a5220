pub const WSOL_MINT: &str = "So11111111111111111111111111111111111111112";
pub const USDC_MINT: &str = "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v";

// 单个Jito小费账户（保留向后兼容）
pub const JITO_TIP_ACCOUNT: &str = "Cw8CFyM9FkoMi7K7Crf6HNQqf4uEMzpKw6QNghXLvLkY";

// 添加Jito小费账户数组，可随机选择
pub const JITO_TIP_ACCOUNTS: [&str; 8] = [
    "ADuUkR4vqLUMWXxW9gh6D6L8pMSawimctcNZ5pGwDcEt",
    "3AVi9Tg9Uo68tJfuvoKvqKNWKkC5wPdSSdeBnizKZ6jT",
    "DttWaMuVvTiduZRnguLF7jNxTgiMBZ1hyAumKUiL2KRL",
    "ADaUMid9yfUytqMBgopwjb2DTLSokTSzL1zt6iGPaS49",
    "DfXygSm4jCyNCybVYYK6DwvWqjKee8pbDmJGcLWNDXjh",
    "HFqU5x63VTqvQss8hp11i4wVV8bD44PvwucfZ2bU7gRe",
    "96gYZGLnJYVFmbjzopPSU6QiEV5fGqZNyN9nmNhvrZU5",
    "Cw8CFyM9FkoMi7K7Crf6HNQqf4uEMzpKw6QNghXLvLkY"
];

// 添加 COIN 代币地址
pub const COIN_MINT: &str = "9BB6NFEcjBCtnNLFko2FqVQBq8HHM13kCyYcdQbgpump"; // 示例代币地址

// 从小号返回到主账户的金额：0.001 SOL
pub const RETURN_AMOUNT: u64 = 1_000_000;

// 网络费用保留金额: 0.000005 SOL
pub const NETWORK_FEE: u64 = 5_000;

// 数据目录路径
pub const DATA_DIR: &str = "./data";

// 小号账户数量
pub const TIP_ACCOUNTS_COUNT: usize = 2000;

// SOL/USDC 价格 (1 SOL = 180 USDC)
pub const SOL_TO_USDC_PRICE: f64 = 153.0;

// 最大小费限制 (0.0019 SOL)
pub const MAX_TIP_LAMPORTS: u64 = 1_900_000;

use lazy_static::lazy_static;
use std::env;
use rand::seq::SliceRandom;
use rand::thread_rng;

lazy_static! {
    pub static ref RPC_URL: String = {
        env::var("RPC_URL").unwrap_or_else(|_| "https://solana-rpc.publicnode.com".to_string())
    };
    pub static ref JUP_V6_API_BASE_URL: String = {
        env::var("JUP_V6_API_BASE_URL")
            .unwrap_or_else(|_| "http://**************:8080".to_string())
    };
    pub static ref JITO_RPC_URL: String = {
        env::var("JITO_RPC_URL")
            .unwrap_or_else(|_| "https://frankfurt.mainnet.block-engine.jito.wtf/api/v1/bundles".to_string())
    };
}

// 获取随机Jito小费地址的函数
pub fn get_random_jito_tip_account() -> &'static str {
    // 每次调用函数时创建一个新的随机数生成器，避免线程安全问题
    let mut rng = thread_rng();
    *JITO_TIP_ACCOUNTS.choose(&mut rng).unwrap()
}
